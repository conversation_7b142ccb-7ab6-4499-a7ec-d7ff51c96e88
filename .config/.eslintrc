/*
 * ⚠️⚠️⚠️ THIS FILE WAS SCAFFOLDED BY `@grafana/create-plugin`. DO NOT EDIT THIS FILE DIRECTLY. ⚠️⚠️⚠️
 *
 * In order to extend the configuration follow the steps in
 * https://grafana.com/developers/plugin-tools/get-started/set-up-development-environment#extend-the-eslint-config
 */
{
  "extends": ["@grafana/eslint-config"],
  "root": true,
  "rules": {
    "react/prop-types": "off"
  },
  "overrides": [
    {
      "files": ["src/**/*.{ts,tsx}"],
      "rules": {
        "@typescript-eslint/no-deprecated": "warn"
      },
      "parserOptions": {
        "project": "./tsconfig.json"
      }
    },
    {
      "files": ["./tests/**/*"],
      "rules": {
        "react-hooks/rules-of-hooks": "off"
      }
    }
  ]
}
