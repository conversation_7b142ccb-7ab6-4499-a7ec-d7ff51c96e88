services:
  grafana:
    user: root
    container_name: 'epropulsion-grafanademo-panel'

    build:
      context: .
      args:
        grafana_image: ${GRAFANA_IMAGE:-grafana-enterprise}
        grafana_version: ${GRAFANA_VERSION:-12.0.2}
        development: ${DEVELOPMENT:-false}
        anonymous_auth_enabled: ${ANONYMOUS_AUTH_ENABLED:-true}
    ports:
      - 3000:3000/tcp
    volumes:
      - ../dist:/var/lib/grafana/plugins/epropulsion-grafanademo-panel
      - ../provisioning:/etc/grafana/provisioning
      - ..:/root/epropulsion-grafanademo-panel

    environment:
      NODE_ENV: development
      GF_LOG_FILTERS: plugin.epropulsion-grafanademo-panel:debug
      GF_LOG_LEVEL: debug
      GF_DATAPROXY_LOGGING: 1
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: epropulsion-grafanademo-panel
