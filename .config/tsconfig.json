/*
 * ⚠️⚠️⚠️ THIS FILE WAS SCAFFOLDED BY `@grafana/create-plugin`. DO NOT EDIT THIS FILE DIRECTLY. ⚠️⚠️⚠️
 *
 * In order to extend the configuration follow the steps in
 * https://grafana.com/developers/plugin-tools/get-started/set-up-development-environment#extend-the-typescript-config
 */
{
  "compilerOptions": {
    "alwaysStrict": true,
    "declaration": false,
    "rootDir": "../src",
    "baseUrl": "../src",
    "typeRoots": ["../node_modules/@types"],
    "resolveJsonModule": true
  },
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs",
      "target": "es5",
      "esModuleInterop": true
    },
    "transpileOnly": true
  },
  "include": ["../src", "./types"],
  "extends": "@grafana/tsconfig"
}
