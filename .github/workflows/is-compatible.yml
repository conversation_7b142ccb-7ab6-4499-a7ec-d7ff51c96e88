name: Latest Grafana API compatibility check
on: [pull_request]

jobs:
  compatibilitycheck:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup Node.js environment
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build plugin
        run: npm run build

      - name: Find module.ts or module.tsx
        id: find-module-ts
        run: |
          MODULETS="$(find ./src -type f \( -name "module.ts" -o -name "module.tsx" \))"
          echo "modulets=${MODULETS}" >> $GITHUB_OUTPUT

      - name: Compatibility check
        uses: grafana/plugin-actions/is-compatible@main
        with:
          module: ${{ steps.find-module-ts.outputs.modulets }}
          comment-pr: 'no'
          fail-if-incompatible: 'yes'
