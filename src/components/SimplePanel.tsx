import React, { useMemo, useState, useEffect } from 'react';
import { PanelProps } from '@grafana/data';
import { SimpleOptions, DEFAULT_SECTION_PARAMS, SectionParameters, SelectedParameters } from 'types';
import { css, cx } from '@emotion/css';
import { useStyles2, useTheme2, MultiSelect } from '@grafana/ui';
import { PanelDataErrorView } from '@grafana/runtime';
import ReactECharts from 'echarts-for-react';

interface Props extends PanelProps<SimpleOptions> {}

const getStyles = () => {
  return {
    wrapper: css`
      font-family: Open Sans;
      position: relative;
      display: flex;
      flex-direction: column;
    `,
    parameterSelector: css`
      padding: 10px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      margin-bottom: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    `,
    selectorItem: css`
      min-width: 200px;
      flex: 1;
    `,
    selectorLabel: css`
      font-weight: 500;
      margin-bottom: 4px;
      font-size: 12px;
    `,
    chartContainer: css`
      width: 100%;
      flex: 1;
    `,
  };
};

// 处理经过Join transformation的单个series（包含多个数值字段）
const handleJoinedSeriesData = (serie: any, options: SimpleOptions, selectedParams: SelectedParameters, defaultColors: string[]) => {
  console.log('处理Join后的数据，字段:', serie.fields?.map((f: any) => ({
    name: f.name,
    displayName: f.displayName,
    type: f.type,
    config: f.config
  })));

  const fields = serie.fields || [];
  
  // 获取时间字段
  const timeField = fields.find((field: any) => field.type === 'time');
  if (!timeField) {
    console.log('未找到时间字段');
    return null;
  }
  
  const timeValues = timeField.values.toArray();
  const xAxisData = timeValues.map((timestamp: number) => new Date(timestamp));
  
  // 获取所有数值字段
  const numberFields = fields.filter((field: any) => field.type === 'number');
  console.log('找到数值字段:', numberFields.map((f: any) => ({
    name: f.name,
    displayName: f.displayName,
    config: f.config
  })));
  
  // 获取所有被选中的参数
  const allSelectedParams = Object.values(selectedParams).flat();
  
  // 过滤匹配的数值字段
  const matchedFields = numberFields.filter((field: any) => {
    const fieldName = field.config?.displayName || field.displayName || field.name || '';
    const normalizedFieldName = fieldName.toLowerCase().trim();
    
    console.log(`检查字段匹配: "${fieldName}" (normalized: "${normalizedFieldName}")`);
    
    const isMatch = allSelectedParams.some(param => {
      const paramName = param.toLowerCase().trim();
      
      // 只使用完全匹配
      const matches = normalizedFieldName === paramName;
      console.log(`  vs "${param}" (完全匹配) = ${matches}`);
      return matches;
    });
    
    console.log(`字段 "${fieldName}" 匹配结果: ${isMatch}`);
    return isMatch;
  });
  
  console.log('匹配的字段:', matchedFields.map((f: any) => f.config?.displayName || f.displayName || f.name));
  
  // 为每个匹配的字段创建chart series
  const chartSeries = matchedFields.map((field: any, index: number) => {
    const values = field.values.toArray();
    const fieldName = field.config?.displayName || field.displayName || field.name || `Field ${index + 1}`;
    
    // 构建[timestamp, value]格式的数据
    const chartData = timeValues.map((timestamp: number, i: number) => [
      timestamp,
      values[i] || 0
    ]);
    
    return {
      name: fieldName,
      type: 'line',
      data: chartData,
      smooth: options.smooth || false,
      lineStyle: {
        width: options.lineWidth || 2,
        color: options.colorPalette?.[index % options.colorPalette.length] || defaultColors[index % defaultColors.length]
      },
      itemStyle: {
        color: options.colorPalette?.[index % options.colorPalette.length] || defaultColors[index % defaultColors.length]
      }
    };
  });
  
  return {
    xAxisData,
    series: chartSeries
  };
};

// 将 Grafana 数据转换为 ECharts 格式，支持参数过滤
const convertGrafanaDataToECharts = (data: any, options: SimpleOptions, selectedParams: SelectedParameters) => {
  const series = data.series || [];
  const defaultColors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'];
  
  // 调试：打印数据结构
  console.log('=== 调试数据结构 ===');
  console.log('series:', series);
  series.forEach((serie: any, index: number) => {
    console.log(`Series ${index}:`, {
      name: serie.name,
      displayName: serie.displayName,
      refId: serie.refId,
      fields: serie.fields?.map((f: any) => ({
        name: f.name,
        displayName: f.displayName,
        type: f.type,
        config: f.config
      }))
    });
  });
  console.log('selectedParams:', selectedParams);
  
  if (series.length === 0) {
    return null;
  }

  // 获取所有被选中的参数
  const allSelectedParams = Object.values(selectedParams).flat();
  
  // 如果没有选中任何参数，不显示任何数据
  if (allSelectedParams.length === 0) {
    return {
      xAxisData: [],
      series: []
    };
  }

  // 处理经过Join transformation的数据结构
  // 这种情况下可能只有一个series，包含多个数值字段
  if (series.length === 1 && series[0].fields && series[0].fields.length > 2) {
    console.log('检测到Join后的数据结构，处理多字段series');
    return handleJoinedSeriesData(series[0], options, selectedParams, defaultColors);
  }

  // 简化匹配逻辑，更宽松的匹配
  const filteredSeries = series.filter((serie: any) => {
    // 获取系列名称，优先使用字段的 displayName
    const numberField = serie.fields?.find((f: any) => f.type === 'number');
    const seriesName = numberField?.config?.displayName || 
                      numberField?.displayName ||
                      numberField?.name ||
                      serie.name || 
                      serie.displayName || 
                      serie.refId || 
                      'Unknown Series';
    
    console.log(`检查系列匹配:`, {
      seriesName,
      numberField: numberField ? {
        name: numberField.name,
        displayName: numberField.displayName,
        config: numberField.config
      } : null,
      allSelectedParams
    });
    
    if (!seriesName || seriesName === 'Unknown Series') {
      console.log('系列名称无效，跳过');
      return false;
    }
    
    const normalizedSeriesName = seriesName.toLowerCase().trim();
    
    // 检查是否匹配任何选中的参数
    const isMatch = allSelectedParams.some(param => {
      const paramName = param.toLowerCase().trim();
      
      // 只使用完全匹配
      const matches = normalizedSeriesName === paramName;
      console.log(`匹配检查: "${normalizedSeriesName}" vs "${paramName}" (完全匹配) = ${matches}`);
      return matches;
    });
    
    console.log(`系列 "${seriesName}" 匹配结果: ${isMatch}`);
    return isMatch;
  });

  return convertAllData(filteredSeries, options, defaultColors);
};

// 转换所有数据的辅助函数
const convertAllData = (series: any[], options: SimpleOptions, defaultColors: string[]) => {
  // 获取时间轴数据 - 从所有series中寻找时间字段，而不是只从第一个
  let timeField = null;
  let timeValues: any[] = [];
  
  // 遍历所有series，找到第一个有效的时间字段
  for (const serie of series) {
    const foundTimeField = serie.fields?.find((field: any) => field.type === 'time');
    if (foundTimeField && foundTimeField.values && foundTimeField.values.length > 0) {
      timeField = foundTimeField;
      timeValues = foundTimeField.values.toArray();
      break;
    }
  }
  
  // 如果没有找到时间字段，尝试从原始数据中获取
  if (!timeField && series.length > 0) {
    // 从第一个series获取时间数据作为fallback
    const fallbackTimeField = series[0]?.fields?.find((field: any) => field.type === 'time');
    if (fallbackTimeField) {
      timeField = fallbackTimeField;
      timeValues = fallbackTimeField.values.toArray();
    }
  }
  
  const xAxisData = timeValues.map((timestamp: number) => new Date(timestamp));

  // 构建系列数据
  const chartSeries = series.map((serie: any, index: number) => {
    const valueField = serie.fields.find((field: any) => field.type === 'number');
    const values = valueField ? valueField.values.toArray() : [];
    
    // 获取当前series的时间字段
    const serieTimeField = serie.fields?.find((field: any) => field.type === 'time');
    const serieTimeValues = serieTimeField ? serieTimeField.values.toArray() : timeValues;
    
    // 使用相同的名称获取逻辑，优先使用字段的 displayName
    const seriesName = valueField?.config?.displayName || 
                      valueField?.displayName ||
                      valueField?.name ||
                      serie.name || 
                      serie.displayName || 
                      serie.refId || 
                      `Series ${index + 1}`;
    
    // 为time类型的x轴构建[timestamp, value]格式的数据
    const chartData = serieTimeValues.map((timestamp: number, i: number) => [
      timestamp, // ECharts时间轴需要时间戳
      values[i] || 0
    ]);
    
    return {
      name: seriesName,
      type: 'line',
      data: chartData,
      smooth: options.smooth || false,
      lineStyle: {
        width: options.lineWidth || 2,
        color: options.colorPalette?.[index % options.colorPalette.length] || defaultColors[index % defaultColors.length]
      },
      itemStyle: {
        color: options.colorPalette?.[index % options.colorPalette.length] || defaultColors[index % defaultColors.length]
      }
    };
  });

  return {
    xAxisData,
    series: chartSeries
  };
};

export const SimplePanel: React.FC<Props> = ({ options, data, width, height, fieldConfig, id, onOptionsChange }) => {
  const theme = useTheme2();
  const styles = useStyles2(getStyles);
  
  // 初始化选择状态
  const [selectedParameters, setSelectedParameters] = useState<SelectedParameters>(
    options.selectedParameters || {
      Base: [],
      Motor: [],
      ECU: [],
      BMS: [],
      'Throttle data': [],
      Helm: [],
      'Electric steering': []
    }
  );
  
  // 同步外部 options 的变化到内部状态
  useEffect(() => {
    if (options.selectedParameters) {
      setSelectedParameters(options.selectedParameters);
    }
  }, [options.selectedParameters]);
  
  // 计算图表数据
  const chartData = useMemo(() => {
    return convertGrafanaDataToECharts(data, options, selectedParameters);
  }, [data, options, selectedParameters]);

  // 处理参数选择变化
  const handleParameterChange = (sectionName: keyof SectionParameters, selectedValues: string[]) => {
    const newSelection = {
      ...selectedParameters,
      [sectionName]: selectedValues
    };
    
    setSelectedParameters(newSelection);
    
    // 更新选项
    if (onOptionsChange) {
      onOptionsChange({
        ...options,
        selectedParameters: newSelection
      });
    }
  };

  // 为每个下拉框准备选项
  const getSelectOptions = (sectionName: keyof SectionParameters) => {
    return DEFAULT_SECTION_PARAMS[sectionName].map(param => ({
      label: param,
      value: param
    }));
  };

  if (data.series.length === 0) {
    return <PanelDataErrorView fieldConfig={fieldConfig} panelId={id} data={data} needsStringField />;
  }

  // ECharts 配置选项
  const chartOption = useMemo(() => {
    if (!chartData) return {};

    return {
      title: {
        text: options.chartTitle || '',
        subtext: options.chartSubtitle || '',
        left: 'center',
        textStyle: {
          color: theme.colors.text.primary,
          fontSize: 16
        },
        subtextStyle: {
          color: theme.colors.text.secondary,
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: theme.colors.background.secondary,
        borderColor: theme.colors.border.strong,
        textStyle: {
          color: theme.colors.text.primary
        },
        formatter: (params: any) => {
          if (!params || params.length === 0) return '';
          
          // 从第一个参数的数据中获取时间戳（因为使用了[timestamp, value]格式）
          const timestamp = params[0].data?.[0] || params[0].axisValue;
          
          // 格式化完整的日期时间，使用用户浏览器时区
          const fullDateTime = timestamp  
            ? new Date(timestamp).toLocaleString(undefined, {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
              })
            : 'Unknown Time';
          
          // 构建tooltip内容
          let content = `<div style="font-weight: bold; margin-bottom: 8px;">${fullDateTime}</div>`;
          
          params.forEach((param: any) => {
            const color = param.color;
            const seriesName = param.seriesName;
            // 从[timestamp, value]格式中获取value部分
            const rawValue = param.data?.[1] ?? param.value;
            const value = typeof rawValue === 'number' ? rawValue.toFixed(2) : rawValue;
            
            content += `
              <div style="margin-bottom: 4px;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
                <span style="font-weight: 500;">${seriesName}:</span>
                <span style="margin-left: 8px; font-weight: bold;">${value}</span>
              </div>
            `;
          });
          
          return content;
        }
      },
      legend: {
        show: options.showLegend !== false,
        top: 'bottom',
        textStyle: {
          color: theme.colors.text.primary
        }
      },
      grid: {
        show: options.showGrid !== false,
        left: '3%',
        right: '4%',
        bottom: options.showLegend !== false ? '15%' : '3%',
        containLabel: true,
        backgroundColor: 'transparent',
        borderColor: theme.colors.border.weak
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        name: options.xAxisLabel || '',
        nameTextStyle: {
          color: theme.colors.text.primary
        },
        axisLabel: {
          color: theme.colors.text.secondary,
          formatter: (value: any) => {
            // ECharts time轴会传递时间戳，转换为Date对象然后格式化
            const date = new Date(value);
            return date.toLocaleTimeString(undefined, {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
          }
        },
        axisLine: {
          lineStyle: {
            color: theme.colors.border.weak
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.colors.border.weak,
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: options.yAxisLabel || '',
        nameTextStyle: {
          color: theme.colors.text.primary
        },
        axisLabel: {
          color: theme.colors.text.secondary
        },
        axisLine: {
          lineStyle: {
            color: theme.colors.border.weak
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.colors.border.weak,
            type: 'dashed'
          }
        }
      },
      series: chartData.series,
      dataZoom: options.showDataZoom ? [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: theme.colors.primary.main
          },
          textStyle: {
            color: theme.colors.text.primary
          }
        }
      ] : undefined,
      backgroundColor: 'transparent'
    };
  }, [chartData, options, theme]);

  return (
    <div
      className={cx(
        styles.wrapper,
        css`
          width: ${width}px;
          height: ${height}px;
        `
      )}
    >
      {/* 参数选择器 */}
      <div className={styles.parameterSelector}>
        {(Object.keys(DEFAULT_SECTION_PARAMS) as Array<keyof SectionParameters>).map((sectionName) => (
          <div key={sectionName} className={styles.selectorItem}>
            <div className={styles.selectorLabel}>{sectionName}</div>
            <MultiSelect
              options={getSelectOptions(sectionName)}
              value={selectedParameters[sectionName].map(val => ({ label: val, value: val }))}
              onChange={(values) => handleParameterChange(sectionName, values.map(v => v.value || ''))}
              placeholder={`选择 ${sectionName} 参数`}
              closeMenuOnSelect={false}
              isClearable={true}
              isSearchable={true}
              width={200}
            />
          </div>
        ))}
      </div>
      
      {/* 图表容器 */}
      <div className={styles.chartContainer}>
        <ReactECharts
          key={JSON.stringify(selectedParameters)}
          option={chartOption}
          style={{ width: '100%', height: '100%' }}
          theme={theme.isDark ? 'dark' : 'light'}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={false}
        />
      </div>
    </div>
  );
};
