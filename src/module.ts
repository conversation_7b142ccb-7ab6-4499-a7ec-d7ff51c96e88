import { PanelPlugin } from '@grafana/data';
import { SimpleOptions } from './types';
import { SimplePanel } from './components/SimplePanel';

export const plugin = new PanelPlugin<SimpleOptions>(SimplePanel).setPanelOptions((builder) => {
  return builder
    .addTextInput({
      path: 'chartTitle',
      name: '图表标题',
      description: '图表的主标题',
      defaultValue: '',
    })
    .addTextInput({
      path: 'chartSubtitle',
      name: '图表副标题',
      description: '图表的副标题',
      defaultValue: '',
    })
    .addTextInput({
      path: 'xAxisLabel',
      name: 'X轴标签',
      description: 'X轴的标签名称',
      defaultValue: '时间',
    })
    .addTextInput({
      path: 'yAxisLabel',
      name: 'Y轴标签',
      description: 'Y轴的标签名称',
      defaultValue: '数值',
    })
    .addBooleanSwitch({
      path: 'showLegend',
      name: '显示图例',
      description: '显示图表图例',
      defaultValue: true,
    })
    .addBooleanSwitch({
      path: 'showGrid',
      name: '显示网格',
      description: '显示图表网格线',
      defaultValue: true,
    })
    .addBooleanSwitch({
      path: 'smooth',
      name: '平滑曲线',
      description: '启用平滑曲线效果',
      defaultValue: false,
    })
    .addBooleanSwitch({
      path: 'showDataZoom',
      name: '数据缩放',
      description: '启用数据缩放功能',
      defaultValue: false,
    })
    .addSliderInput({
      path: 'lineWidth',
      name: '线条宽度',
      description: '折线图的线条宽度',
      defaultValue: 2,
      settings: {
        min: 1,
        max: 10,
        step: 1,
      },
    });
});
