// 定义各板块的参数
export interface SectionParameters {
  Base: string[];
  Motor: string[];
  ECU: string[];
  BMS: string[];
  'Throttle data': string[];
  Helm: string[];
  'Electric steering': string[];
}

// 默认参数配置
export const DEFAULT_SECTION_PARAMS: SectionParameters = {
  Base: ['12v battery voltage', 'Pump speed', 'Kw consumption'],
  Motor: ['Busbar Voltage', 'RPM', 'Power', 'Temp', 'Current'],
  ECU: ['Speed', 'Sat signal quality', 'Cooling pump/fan active?'],
  BMS: ['SOC', 'SOH', 'Charging gun state', 'Consumption', 'Cell temp', 'Relay status', 'Cell voltage', 'Power', 'Total voltage', 'Total current'],
  'Throttle data': ['Button press', 'Throttle lever position', 'Killcord status', 'Gear status F/N/R', 'Trim button up /down', 'Dock button / mode status', 'Turbo button / mode status', '1Lever button / mode status'],
  Helm: ['Steering wheel angle'],
  'Electric steering': ['Angle', 'Current phase/bus??', 'Motor speed']
};

// 选择状态接口
export interface SelectedParameters {
  Base: string[];
  Motor: string[];
  ECU: string[];
  BMS: string[];
  'Throttle data': string[];
  Helm: string[];
  'Electric steering': string[];
}

export interface SimpleOptions {
  // ECharts 配置选项
  chartTitle: string;
  chartSubtitle: string;
  xAxisLabel: string;
  yAxisLabel: string;
  showLegend: boolean;
  showDataZoom: boolean;
  smooth: boolean;
  showGrid: boolean;
  lineWidth: number;
  colorPalette: string[];
  // 参数选择配置
  selectedParameters: SelectedParameters;
}
